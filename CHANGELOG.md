# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.4] - 2025-07-16

### Fixed
- Fixed empty billing data when creating card token with a guest user
- Updated PayID instructions text from "Scan to pay" to "Pay" for better user experience

---

## [1.0.3] - 2025-07-14

### Added
- Integrated Card SDK and PayID instruction section directly into WooCommerce checkout flow
- Improved PayID flow with enhanced reusability - generate and reuse 1 PayID for up to 10 orders instead of creating new PayID for each order
- Enhanced webhook processing for PayID overpayment/underpayment scenarios via NPPRejection handling
- **Auto-navigation to Order Received page** - After successful PayID payment confirmation, users are automatically redirected to the Order Received page after a 5-second countdown

### Fixed
- Fixed scroll broken UI in the Express checkout dialog
- Fixed webhook handler for PayID payments in case of overpayment/underpayment - now properly handled by NPPRejection instead of processing as successful payment
- Improved payment flow stability and user experience during checkout

### Changed
- Removed redirect page dependency for Card SDK integration
- Moved PayID instructions from Order Received page to checkout page for better user experience
- Updated payment processing logic to handle edge cases more reliably
- **Enhanced payment success flow** - Payment confirmation now includes visual countdown timer and automatic navigation to order details

### Improved
- Enhanced checkout experience with inline payment processing
- Better error handling and user feedback during payment flows
- Optimized PayID generation and management for better performance
- **Streamlined post-payment experience** - Users no longer need to manually navigate to order details after payment confirmation

---

## [1.0.2] - Previous Release
*Previous changelog entries would go here*

## [1.0.1] - Previous Release
*Previous changelog entries would go here*

