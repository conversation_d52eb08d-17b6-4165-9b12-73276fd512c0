/**
 * Monoova Payments for WooCommerce - Frontend Styles
 */

/* Card Element Container */
#monoova-card-element {
    margin-bottom: 1.5em;
}

.monoova-card-fields-container {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px 0;
    margin-top: 15px;
}

/* Card Element Styles */
#monoova-card-element-container {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    margin-bottom: 12px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

#monoova-card-element-container:focus-within {
    border-color: #999;
    box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.1);
}

#monoova-card-element-container.StripeElement--focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* Card Fields */
.monoova-card-field {
    background-color: white;
    padding: 10px 15px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    margin-bottom: 10px;
    height: 40px;
    box-sizing: border-box;
    transition: border-color 0.15s ease-in-out;
}

.monoova-card-field.focused {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.monoova-card-field.valid {
    border-color: #28a745;
}

.monoova-card-field.invalid {
    border-color: #dc3545;
}

/* Error Messages */
#monoova-card-errors {
    color: #e25950;
    margin-top: 8px;
    font-size: 0.9em;
}

/* Save Card Option */
.monoova-save-card-option {
    margin-top: 15px;
    margin-bottom: 16px;
}

/* Wallet Button Container */
#monoova-wallet-buttons-container {
    margin-top: 20px;
    margin-bottom: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

/* Wallet Buttons Divider */
.monoova-wallet-divider {
    position: relative;
    margin: 20px 0;
    text-align: center;
    color: #777;
}

.monoova-wallet-divider::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 100%;
    height: 1px;
    background-color: #e0e0e0;
    z-index: 0;
}

.monoova-wallet-divider span {
    background-color: #fff;
    padding: 0 10px;
    position: relative;
    z-index: 1;
    color: #6d6d6d;
    font-size: 0.9em;
}

.monoova-wallet-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
}

.monoova-wallet-button {
    width: 100%;
    height: 45px;
    border-radius: 4px;
    overflow: hidden;
}

/* Apple Pay button container */
#monoova-apple-pay-button {
    -webkit-appearance: -apple-pay-button;
    -apple-pay-button-style: black;
    -apple-pay-button-type: plain;
}

/* Google Pay button container */
#monoova-google-pay-button {
    background-color: black;
    background-origin: content-box;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: contain;
    border: 0px;
    border-radius: 4px;
    box-shadow: rgba(60, 64, 67, 0.3) 0px 1px 1px 0px, rgba(60, 64, 67, 0.15) 0px 1px 3px 1px;
    cursor: pointer;
    min-height: 48px;
    padding: 0px;
    width: 100%;
}

/* PayID Element */
#monoova-payid-element {
    margin-bottom: 1.5em;
}

.monoova-payid-type-selector {
    margin-top: 15px;
}

/* Checkout Loading State */
.monoova-processing {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    color: #3c434a;
}

.monoova-processing:before {
    content: "";
    width: 20px;
    height: 20px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-top: 2px solid #3498db;
    border-radius: 50%;
    margin-right: 10px;
    animation: monoova-spinner 0.8s linear infinite;
}

@keyframes monoova-spinner {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Payment Instructions */
.monoova-payment-instructions {
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f8f8f8;
    margin: 20px 0;
}

.monoova-payment-details {
    list-style: none;
    padding: 0;
    margin: 15px 0;
}

.monoova-payment-details li {
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
}

.monoova-payment-details li:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.monoova-reference {
    font-weight: 700;
    font-family: monospace;
    font-size: 1.1em;
    letter-spacing: 1px;
}

.monoova-reference-note {
    margin-top: 10px;
    margin-bottom: 15px;
    padding: 10px;
    background-color: #fffbcc;
    border: 1px solid #e6db55;
    border-radius: 3px;
}

.monoova-copy-button {
    display: inline-block;
    margin-left: 5px;
    padding: 2px 8px;
    background: #f0f0f0;
    border-radius: 3px;
    font-size: 12px;
    text-decoration: none !important;
    color: #333 !important;
}

.monoova-copy-button:hover {
    background: #e6e6e6;
}

.monoova-expiry-info.monoova-expired {
    color: #e01e5a;
    font-weight: bold;
}

/* Credit Card Icons */
.payment_method_monoova_card img {
    max-height: 24px;
    margin: 0 3px;
}

/* Payment Method Selection */
.wc_payment_method.payment_method_monoova_card .payment_box {
    padding-top: 10px;
}

.wc_payment_method.payment_method_monoova_payid .monoova-payment-notice {
    background-color: #f9f9f9;
    padding: 10px;
    border-radius: 4px;
    border-left: 4px solid #2271b1;
    margin-bottom: 10px;
}

/* Order Details */
.woocommerce-order-overview__monoova-details {
    margin-top: 20px !important;
}

/* Saved Payment Methods */
.woocommerce-SavedPaymentMethods-tokenInput {
    margin-right: 5px !important;
}

.woocommerce-SavedPaymentMethods-token {
    margin-bottom: 10px;
}

.woocommerce-SavedPaymentMethods-methods {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.woocommerce-SavedPaymentMethods-methods label {
    display: block;
    margin-bottom: 5px;
    cursor: pointer;
}

.woocommerce-SavedPaymentMethods-methods input[type="radio"] {
    vertical-align: middle;
    margin-right: 10px;
}

.woocommerce-SavedPaymentMethods-methods img {
    max-height: 24px;
    vertical-align: middle;
    margin-left: 5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    #monoova-wallet-buttons-container {
        flex-direction: column;
    }
    
    .monoova-apple-pay-button,
    .monoova-google-pay-button {
        max-width: 100%;
    }

    .monoova-wallet-divider::before,
    .monoova-wallet-divider::after {
        width: calc(50% - 15px);
    }

    .monoova-wallet-buttons {
        gap: 8px;
    }
    
    .monoova-wallet-button {
        height: 40px;
    }
}

/* Thank you page styling for payment details */
.woocommerce-order-received .monoova-payment-instructions {
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Test mode notice */
.monoova-test-mode-notice {
    background-color: #fcf8e3;
    border: 1px solid #faebcc;
    padding: 10px;
    margin-bottom: 15px;
    border-radius: 4px;
    color: #8a6d3b;
    font-size: 0.9em;
}

/* Payment Method Options */
.monoova-payment-methods {
    margin: 15px 0;
}

.monoova-payment-method-option {
    display: inline-block;
    margin-right: 15px;
    margin-bottom: 10px;
    cursor: pointer;
}

.monoova-payment-method-option .payment-method-label {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
    transition: all 0.2s ease;
}

.monoova-payment-method-option .payment-method-label img {
    height: 24px;
    margin-right: 8px;
}

.monoova-payment-method-option input[type="radio"]:checked + .payment-method-label {
    border-color: #2ab5c4;
    background: #f8f9fa;
    box-shadow: 0 0 0 1px #2ab5c4;
}

.monoova-payment-method-option:hover .payment-method-label {
    border-color: #2ab5c4;
}

/* Primer Checkout Container */
.checkout-container {
    margin-top: 20px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
}

/* Express Checkout Styles */
/* .wc-block-components-express-payment {
    margin-bottom: 20px;
}

.wc-block-components-express-payment__content {
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: #f8f9fa;
} */

.monoova-express-checkout-button {
    width: 100%;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 0 16px;
    box-sizing: border-box;
    transition: all 0.2s ease;
    background-color: #2271b1;
    color: #fff;
    min-height: 48px;
}

.monoova-express-checkout-button:hover {
    opacity: 0.9;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.monoova-express-checkout-button:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.monoova-express-checkout-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.monoova-express-checkout-button.secondary {
    background-color: #000;
}

.monoova-express-checkout-button .monoova-logo {
    height: 20px;
    width: auto;
}

.monoova-express-checkout-processing {
    position: relative;
    overflow: hidden;
}

.monoova-express-checkout-processing::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Express Checkout in Block Editor */

/* Express Checkout Loading State */
.monoova-express-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #666;
    font-style: italic;
}

.monoova-express-loading::before {
    content: '';
    width: 16px;
    height: 16px;
    margin-right: 8px;
    border: 2px solid #e1e1e1;
    border-top: 2px solid #2271b1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Express Checkout Styles */


.monoova-express-checkout-button {
    width: 100%;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 0 16px;
    box-sizing: border-box;
    transition: all 0.2s ease;
    background-color: #2271b1;
    color: #fff;
    min-height: 48px;
}

.monoova-express-checkout-button:hover {
    opacity: 0.9;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.monoova-express-checkout-button:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.monoova-express-checkout-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.monoova-express-checkout-button.secondary {
    background-color: #000;
}

.monoova-express-checkout-button .monoova-logo {
    height: 20px;
    width: auto;
}

.monoova-express-checkout-processing {
    position: relative;
    overflow: hidden;
}

.monoova-express-checkout-processing::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Monoova Express Checkout Modal */
.monoova-express-payment-modal {
    z-index: 999999;
}

.monoova-express-payment-modal .components-modal__content {
    width: 500px;
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
}

.monoova-express-payment-modal .components-modal__header {
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.monoova-express-payment-modal .components-modal__header h1 {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    color: #1e1e1e;
}

/* Express Payment Button */
.monoova-express-pay-button {
    transition: all 0.2s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.monoova-express-pay-button:hover:not(:disabled) {
    opacity: 0.9;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.monoova-express-pay-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.monoova-express-pay-button--preview {
    cursor: default;
}

.monoova-express-pay-button--preview:hover {
    transform: none;
    box-shadow: none;
}

/* Primer Container Styling */
#primer-express-checkout {
    min-height: 300px;
    margin-top: 10px;
}

/* Loading state */
.monoova-payment-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
    color: #666;
}

.monoova-payment-loading .spinner {
    width: 24px;
    height: 24px;
    border: 2px solid #e0e0e0;
    border-top: 2px solid #2271b1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Mobile responsive */
@media (max-width: 768px) {
    .monoova-express-payment-modal .components-modal__content {
        width: 95vw;
        margin: 20px auto;
    }
    
    .monoova-express-pay-button {
        height: 52px !important;
        font-size: 16px;
    }
}