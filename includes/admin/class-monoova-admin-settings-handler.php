<?php

/**
 * Monoova Admin Settings Handler
 * 
 * Handles AJAX requests for saving unified payment gateway settings
 * 
 * @package Monoova_Payments_For_WooCommerce
 * @since 1.0.0
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Monoova Admin Settings Handler Class
 */
class Monoova_Admin_Settings_Handler {

    /**
     * Debug Mode
     *
     * @var bool
     */
    private $debug = false;

    /**
     * Logger instance
     *
     * @var WC_Logger
     */
    private $logger;

    /**
     * Constructor
     */
    public function __construct() {
        // Enable debug mode based on unified gateway settings
        $this->init_debug_mode();

        // Initialize logger if debug is enabled
        if ($this->debug) {
            $this->logger = wc_get_logger();
        }

        add_action('wp_ajax_monoova_save_payment_settings', array($this, 'save_payment_settings'));
        add_action('wp_ajax_monoova_generate_automatcher', array($this, 'generate_automatcher_account'));
        // check if all webhook subscriptions for Monoova (Card, PayTo, and Payments) is active
        add_action('wp_ajax_monoova_check_webhook_subscriptions_status', array($this, 'check_webhook_subscriptions_status'));
        // subscribe to all necessary webhook subscriptions for Monoova (Card, PayTo, and Payments)
        add_action('wp_ajax_monoova_subscribe_to_webhook_events', array($this, 'subscribe_to_webhook_events'));
    }

    public function check_webhook_subscriptions_status() {
        // to check if all webhook subscriptions for Monoova (Card, PayTo, and Payments) is active, we need to check the following:
        // 1. for Card and PayTo, we need to call API get_all_card_and_payto_webhook_subscriptions() and check if subscriptionDetails field is not empty array
        // With Card, 
        // if subscriptionDetails include both 2 events having eventName 'CreditCardPaymentNotification' and 'CreditCardRefundNotification'
        // --> if subscriptionDetails->{event}->webHookDetail->callBackUrl must be the same as the webhook URL of the site (home_url('/?wc-api=monoova_webhook')) and subscriptionDetails->{event}->isActive must be true
        // ---> Card subscriptions is all active
        // else Card subscriptions is inactive
        
        // With PayTo,
        // if subscriptionDetails include both 2 events having eventName 'PaymentAgreementNotification' and 'PaymentInstructionNotification'
        // --> if subscriptionDetails->{event}->webHookDetail->callBackUrl must be the same as the webhook URL of the site (home_url('/?wc-api=monoova_webhook')) and subscriptionDetails->{event}->isActive must be true
        // ---> PayTo subscriptions is all active
        // else PayTo subscriptions is inactive
        
        // 2. for Payments, we need to call API get_all_payments_webhook_subscriptions() and check if eventName field is not empty array
        // eventName array must include all events having eventName field (eventName->{event}->eventName): NppReturn, NppPaymentStatus, PayToReceivePayment, NPPReceivePayment, InboundDirectCredit, NPPCreditRejections, InboundDirectCreditRejections
        // --> if eventName->{event}->targetUrl must be the same as the webhook URL of the site (home_url('/?wc-api=monoova_webhook')) and eventName->{event}->status must be 'On'
        // ---> Payments subscriptions is all active
        // else Payments subscriptions is inactive

        // if all 3 are active, return true
        // else return false
    }

    public function subscribe_to_webhook_events() {
        // to subscribe to all necessary webhook events for Monoova (Card, PayTo, and Payments), we need to do the following:
        // Step 0: In the beginning, we first must use API get_all_card_and_payto_webhook_subscriptions() to load all existing subscriptions for Card and PayTo (use later to check if any Card/PayTo subscription already exist)
        // Step 1. for Card and PayTo, we need to call API create_subscription_for_card_payto_notification() to create the necessary subscriptions:
            // With Card, we need to create 2 subscriptions: 
            // 1. eventName: CreditCardPaymentNotification, subscriptionName: 'CreditCardPaymentStatusEvent'
            // 2. eventName: CreditCardRefundNotification, subscriptionName: 'CreditCardRefundStatusEvent'
            // With PayTo, we need to create 2 subscriptions: 
            // 1. eventName: PaymentAgreementNotification, subscriptionName: 'PaymentAgreementStatusEvent'
            // 2. eventName: PaymentInstructionNotification, subscriptionName: 'PaymentInstructionStatusEvent'
        // For Card and PayTo, if any of the subscription API throws error code 400:
            // 1. get eventName from the request data of API create_subscription_for_card_payto_notification() and use it to find the matching subscription in the list of all Card and PayTo subscriptions from Step 0) -> get subscriptionId
            // 2. use subscriptionId to call API update_subscription_for_card_notification() to update the subscription (use the same payload as the one used in create_subscription_for_card_payto_notification())
        // Step 2. for Payments, we need to call API create_subscription_for_payments_notification() to create the necessary subscriptions:
            // 1. eventName: NppReturn
            // 2. eventName: NppPaymentStatus
            // 3. eventName: PayToReceivePayment
            // 4. eventName: NPPReceivePayment
            // 5. eventName: InboundDirectCredit
            // 6. eventName: NPPCreditRejections
            // 7. eventName: InboundDirectCreditRejections
            // if any of the subscription API throws error code 400:
            // 1. with Payment subscription API, the error response of 400 will have id in the response. Use the id to call API update_subscription_for_payments_notification() to update the subscription (use the same payload as the one used in create_subscription_for_payments_notification())

        // 
    }
        

    /**
     * Initialize debug mode from gateway settings
     */
    private function init_debug_mode() {
        // Check debug setting from unified gateway first
        $unified_settings = get_option('woocommerce_monoova_unified_settings', array());
        if (isset($unified_settings['debug']) && $unified_settings['debug'] === 'yes') {
            $this->debug = true;
            return;
        }

        // Fallback to card gateway debug setting
        $card_settings = get_option('woocommerce_monoova_card_settings', array());
        if (isset($card_settings['debug']) && $card_settings['debug'] === 'yes') {
            $this->debug = true;
            return;
        }

        // Fallback to payid gateway debug setting
        $payid_settings = get_option('woocommerce_monoova_payid_settings', array());
        if (isset($payid_settings['debug']) && $payid_settings['debug'] === 'yes') {
            $this->debug = true;
        }
    }

    /**
     * Logging method - writes to separate log file for admin settings
     *
     * @param string $message Log message.
     * @param string $level   Optional. Default 'info'. Possible values: emergency, alert, critical, error, warning, notice, info, debug.
     */
    private function log($message, $level = 'info') {
        if ($this->debug && $this->logger) {
            $formatted_message = '[Admin Settings] ' . $message;
            $this->logger->log($level, $formatted_message, array('source' => 'monoova-admin-settings'));
        }
    }

    /**
     * Handle AJAX request to save payment settings
     */
    public function save_payment_settings() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'monoova_save_settings')) {
            $this->log('Nonce verification failed', 'error');
            wp_send_json_error(array('message' => __('Security check failed.', 'monoova-payments-for-woocommerce')));
            return;
        }

        // Check user capabilities
        if (!current_user_can('manage_woocommerce')) {
            $this->log('User capabilities check failed', 'error');
            wp_send_json_error(array('message' => __('Insufficient permissions.', 'monoova-payments-for-woocommerce')));
            return;
        }

        // Get settings from request
        $settings = json_decode(stripslashes($_POST['settings']), true);
        $tab = sanitize_text_field($_POST['tab']);

        $this->log('Decoded settings: ' . print_r($settings, true), 'debug');

        if (!is_array($settings)) {
            wp_send_json_error(array('message' => __('Invalid settings data.', 'monoova-payments-for-woocommerce')));
            return;
        }

        try {
            // Save settings based on tab or save all if tab is 'all'
            switch ($tab) {
                case 'general_settings':
                    $this->log('Saving general settings');
                    $this->save_general_settings($settings);
                    break;
                case 'payment_methods':
                    $this->log('Saving payment methods settings');
                    $this->save_payment_methods_settings($settings);
                    break;
                case 'card_settings':
                    $this->log('Saving card settings');
                    $this->save_card_settings($settings);
                    break;
                case 'payid_settings':
                    $this->log('Saving PayID settings');
                    $this->save_payid_settings($settings);
                    break;
                case 'all':
                default:
                    $this->log('Saving all settings');
                    $this->save_general_settings($settings);
                    $this->save_payment_methods_settings($settings);
                    $this->save_card_settings($settings);
                    $this->save_payid_settings($settings);
                    break;
            }

            $this->log('Settings saved successfully for tab: ' . $tab);

            // Force refresh of gateway instances after saving
            $this->refresh_gateway_instances();

            wp_send_json_success(array('message' => __('Settings saved successfully!', 'monoova-payments-for-woocommerce')));
        } catch (Exception $e) {
            $this->log('Save error: ' . $e->getMessage(), 'error');
            wp_send_json_error(array('message' => $e->getMessage()));
        }
    }

    /**
     * Save payment methods settings (Tab 1)
     */
    private function save_payment_methods_settings($settings) {

        // Update card gateway enabled status
        $card_settings = get_option('woocommerce_monoova_card_settings', array());

        // Explicitly check for false to ensure 'no' is set for checkboxes that are unchecked
        $card_settings['enabled'] = isset($settings['enable_card_payments']) && $settings['enable_card_payments'] ? 'yes' : 'no';
        $card_settings['enable_express_checkout'] = isset($settings['enable_express_checkout']) && $settings['enable_express_checkout'] ? 'yes' : 'no';
        update_option('woocommerce_monoova_card_settings', $card_settings);

        // Update payid gateway enabled status
        $payid_settings = get_option('woocommerce_monoova_payid_settings', array());
        $payid_settings['enabled'] = isset($settings['enable_payid_payments']) && $settings['enable_payid_payments'] ? 'yes' : 'no';
        update_option('woocommerce_monoova_payid_settings', $payid_settings);
    }

    /**
     * Save general settings (General Settings Tab)
     */
    private function save_general_settings($settings) {
        // Update unified gateway settings (enabled and testmode settings)
        $unified_settings = get_option('woocommerce_monoova_unified_settings', array());

        // Save the enabled status for unified gateway
        if (isset($settings['enabled'])) {
            $unified_settings['enabled'] = $settings['enabled'] ? 'yes' : 'no';
        }

        // Save the testmode setting for unified gateway
        // if (isset($settings['testmode'])) {
        //     $unified_settings['testmode'] = $settings['testmode'] ? 'yes' : 'no';
        // }

        update_option('woocommerce_monoova_unified_settings', $unified_settings);

        // Save common settings to both card and payid gateways
        $this->save_common_settings_to_gateways($settings);
    }

    /**
     * Save card settings (Tab 2)
     */
    private function save_card_settings($settings) {
        $card_settings = get_option('woocommerce_monoova_card_settings', array());

        // Card-specific settings including title and description
        $card_fields = array(
            'title' => 'string',
            'description' => 'textarea',
            'testmode' => 'boolean',
            'debug' => 'boolean',
            'capture' => 'boolean',
            'saved_cards' => 'boolean',
            'apply_surcharge' => 'boolean',
            'surcharge_amount' => 'float',
            'enable_apple_pay' => 'boolean',
            'enable_google_pay' => 'boolean',
            'order_button_text' => 'string'
        );

        foreach ($card_fields as $field => $type) {
            // Map card_title to title and card_description to description
            $setting_key = $field;
            if ($field === 'title' && isset($settings['card_title'])) {
                $value = $settings['card_title'];
            } elseif ($field === 'description' && isset($settings['card_description'])) {
                $value = $settings['card_description'];
            } elseif ($field === 'testmode' && isset($settings['card_testmode'])) {
                $value = $settings['card_testmode'];
            } elseif ($field === 'debug' && isset($settings['card_debug'])) {
                $value = $settings['card_debug'];
            } elseif (isset($settings[$field])) {
                $value = $settings[$field];
            } else {
                continue;
            }

            switch ($type) {
                case 'boolean':
                    $card_settings[$field] = $value ? 'yes' : 'no';
                    break;
                case 'float':
                    $card_settings[$field] = (float) $value;
                    break;
                case 'textarea':
                    $card_settings[$field] = sanitize_textarea_field($value);
                    break;
                case 'string':
                default:
                    $card_settings[$field] = sanitize_text_field($value);
                    break;
            }
        }

        // Handle checkout_ui_styles nested object
        if (isset($settings['checkout_ui_styles']) && is_array($settings['checkout_ui_styles'])) {
            $card_settings['checkout_ui_styles'] = $this->sanitize_checkout_ui_styles($settings['checkout_ui_styles']);
        }

        update_option('woocommerce_monoova_card_settings', $card_settings);
    }

    /**
     * Save PayID settings (Tab 3)
     */
    private function save_payid_settings($settings) {
        $payid_settings = get_option('woocommerce_monoova_payid_settings', array());

        // PayID-specific settings including title and description
        $payid_settings = get_option('woocommerce_monoova_payid_settings', array());

        // PayID-specific settings including title and description
        $payid_fields = array(
            'title' => 'string',
            'description' => 'textarea',
            'testmode' => 'boolean',
            'debug' => 'boolean',
            'static_bank_account_name' => 'string',
            'static_bsb' => 'string',
            'static_account_number' => 'string',
            'expire_hours' => 'integer',
            'account_name' => 'string',
            'instructions' => 'textarea',
            'payid_show_reference_field' => 'boolean',
        );

        foreach ($payid_fields as $field => $type) {
            // Map payid_title to title and payid_description to description
            $setting_key = $field;
            if ($field === 'title' && isset($settings['payid_title'])) {
                $value = $settings['payid_title'];
            } elseif ($field === 'description' && isset($settings['payid_description'])) {
                $value = $settings['payid_description'];
            } elseif ($field === 'testmode' && isset($settings['payid_testmode'])) {
                $value = $settings['payid_testmode'];
            } elseif ($field === 'debug' && isset($settings['payid_debug'])) {
                $value = $settings['payid_debug'];
            } elseif (isset($settings[$field])) {
                $value = $settings[$field];
            } else {
                continue;
            }

            switch ($type) {
                case 'boolean':
                    $payid_settings[$field] = $value ? 'yes' : 'no';
                    break;
                case 'integer':
                    $payid_settings[$field] = intval($value);
                    break;
                case 'textarea':
                    $payid_settings[$field] = sanitize_textarea_field($value);
                    break;
                case 'string':
                default:
                    $payid_settings[$field] = sanitize_text_field($value);
                    break;
            }
        }

        // Handle payment_types array
        if (isset($settings['payment_types']) && is_array($settings['payment_types'])) {
            $payid_settings['payment_types'] = array_map('sanitize_text_field', $settings['payment_types']);
        }

        update_option('woocommerce_monoova_payid_settings', $payid_settings);
    }

    /**
     * Save common settings shared between gateways
     */
    private function save_common_settings_to_gateways($settings) {
        // Save common settings to both gateways
        $common_fields = array(
            'maccount_number' => 'string',
            'test_api_key' => 'string',
            'live_api_key' => 'string',
            'monoova_payments_api_url_sandbox' => 'string',
            'monoova_payments_api_url_live' => 'string',
            'monoova_card_api_url_sandbox' => 'string',
            'monoova_card_api_url_live' => 'string'
        );

        // Also handle testmode as a common boolean setting
        $common_boolean_fields = array(
            'testmode' => 'boolean'
        );

        $card_settings = get_option('woocommerce_monoova_card_settings', array());
        $payid_settings = get_option('woocommerce_monoova_payid_settings', array());
        $unified_settings = get_option('woocommerce_monoova_unified_settings', array());

        // Handle string fields
        foreach ($common_fields as $field => $type) {
            if (isset($settings[$field])) {
                $value = sanitize_text_field($settings[$field]);

                $card_settings[$field] = $value;
                $payid_settings[$field] = $value;
                $unified_settings[$field] = $value;
            }
        }

        // Handle boolean fields
        foreach ($common_boolean_fields as $field => $type) {
            if ($field === 'testmode') {
                // Skip unified testmode - each gateway will handle its own testmode independently
                continue;
            }
            
            $value = isset($settings[$field]) && $settings[$field] ? 'yes' : 'no';

            $card_settings[$field] = $value;
            $payid_settings[$field] = $value;
            $unified_settings[$field] = $value;
        }

        update_option('woocommerce_monoova_card_settings', $card_settings);
        update_option('woocommerce_monoova_payid_settings', $payid_settings);
        update_option('woocommerce_monoova_unified_settings', $unified_settings);

        // Add debug logging
        // if ($this->debug) {
        //     $this->log('Common settings saved to all gateways');
        //     $this->log('Card settings: ' . print_r($card_settings, true));
        //     $this->log('PayID settings: ' . print_r($payid_settings, true));
        //     $this->log('Unified settings: ' . print_r($unified_settings, true));
        // }

        // Force refresh of gateway instances to pick up new settings
        $this->refresh_gateway_instances();
    }

    /**
     * Sanitize checkout UI styles nested object
     */
    private function sanitize_checkout_ui_styles($styles) {
        $sanitized = array();

        // Define the expected structure
        $expected_structure = array(
            'input_label' => array(
                'font_family' => 'string',
                'font_weight' => 'string',
                'font_size' => 'string',
                'color' => 'string'
            ),
            'input' => array(
                'font_family' => 'string',
                'font_weight' => 'string',
                'font_size' => 'string',
                'background_color' => 'string',
                'border_color' => 'string',
                'border_radius' => 'string',
                'text_color' => 'string'
            ),
            'submit_button' => array(
                'font_family' => 'string',
                'font_size' => 'string',
                'background' => 'string',
                'border_radius' => 'string',
                'border_color' => 'string',
                'font_weight' => 'string',
                'text_color' => 'string'
            )
        );

        foreach ($expected_structure as $section => $fields) {
            if (isset($styles[$section]) && is_array($styles[$section])) {
                $sanitized[$section] = array();
                foreach ($fields as $field => $type) {
                    if (isset($styles[$section][$field])) {
                        $sanitized[$section][$field] = sanitize_text_field($styles[$section][$field]);
                    }
                }
            }
        }

        return $sanitized;
    }

    /**
     * Force refresh of gateway instances to pick up new settings
     */
    private function refresh_gateway_instances() {
        // Clear any cached gateway instances
        if (function_exists('wp_cache_delete')) {
            wp_cache_delete('woocommerce_payment_gateways', 'woocommerce');
        }

        // Force WooCommerce to reload payment gateways
        if (class_exists('WC_Payment_Gateways')) {
            WC_Payment_Gateways::instance()->init();
        }
    }

    /**
     * Handle AJAX request to generate a store-wide Automatcher account.
     */
    public function generate_automatcher_account() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'monoova_generate_automatcher_nonce')) {
            $this->log('Generate Automatcher: Nonce verification failed', 'error');
            wp_send_json_error(array('message' => __('Security check failed.', 'monoova-payments-for-woocommerce')));
            return;
        }

        // Check user capabilities
        if (!current_user_can('manage_woocommerce')) {
            $this->log('Generate Automatcher: User capabilities check failed', 'error');
            wp_send_json_error(array('message' => __('Insufficient permissions.', 'monoova-payments-for-woocommerce')));
            return;
        }

        $this->log('Attempting to generate a new store-wide Automatcher account.');

        try {
            // We need an API client. We can get it from one of the gateways.
            $payid_gateway = new Monoova_PayID_Gateway();
            $api = $payid_gateway->get_api();

            // Get the account name from saved settings, fallback to site name
            $payid_settings = get_option('woocommerce_monoova_payid_settings', array());
            $bank_account_name = !empty($payid_settings['account_name']) ? sanitize_text_field($payid_settings['account_name']) : get_bloginfo('name');

            // Create a unique reference for this one-time account creation
            $client_unique_id = 'WC' . time();

            $payload = array(
                "clientUniqueId" => $client_unique_id,
                "bankAccountName" => $bank_account_name,
                "isActive" => true, // Request it to be active
            );

            $this->log('Payload for /receivables/v1/create (Automatcher): ' . wp_json_encode($payload), 'debug');
            $response = $api->create_receivables_account($payload);

            if (is_wp_error($response) || !isset($response['status']) || strtolower($response['status']) !== 'ok') {
                $error_msg = is_wp_error($response) ? $response->get_error_message() : ($response['statusDescription'] ?? 'Failed to create Automatcher account.');
                throw new Exception($error_msg);
            }

            $this->log('Successfully created new Automatcher account via API: ' . wp_json_encode($response), 'info');

            $new_bsb = $response['bsb'] ?? null;
            $new_account_number = $response['bankAccountNumber'] ?? null;

            if (!$new_bsb || !$new_account_number) {
                throw new Exception(__('API response was successful but did not contain BSB or Account Number.', 'monoova-payments-for-woocommerce'));
            }

            // Save the new details to the PayID settings
            $payid_settings['static_bank_account_name'] = $response['bankAccountName'] ?? $bank_account_name;
            $payid_settings['static_bsb'] = $new_bsb;
            $payid_settings['static_account_number'] = $new_account_number;
            update_option('woocommerce_monoova_payid_settings', $payid_settings);

            wp_send_json_success(array(
                'message' => __('New Automatcher account created and saved successfully! Please note it may take up to 5 minutes to become fully active for receiving payments.', 'monoova-payments-for-woocommerce'),
                'bsb' => $new_bsb,
                'accountNumber' => $new_account_number,
                'accountName' => $payid_settings['static_bank_account_name'],
            ));
        } catch (Exception $e) {
            $this->log('Generate Automatcher error: ' . $e->getMessage(), 'error');
            wp_send_json_error(array('message' => $e->getMessage()));
        }
    }
}

// Initialize the handler
new Monoova_Admin_Settings_Handler();
